## Theme Code Review Report

This report summarizes the changes identified between the `feature/theme` branch and the `main` branch, categorizing them as theme-related or non-theme-related.

### Summary

- **Total Files Modified in Branch:** 519
- **Files Reviewed in Detail:** 70
- **Theme-Only Changes:** 22
- **Contains Non-Theme Modifications:** 48
- **Review Status:** Core theme files and representative samples completed

---

### Detailed File Review

#### File: `assets/images/mec_login_logo_darkmode.svg`

- **Change Description:** New file added. This SVG image is likely a dark mode version of a login logo.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** This file is a new asset specifically for dark mode, making it a theme-only change.

#### File: `assets/translations/ar.json`

- **Change Description:** Added a new translation key "dark_theme" with the Arabic translation "الوضع الداكن".
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** This is a text content change directly related to supporting theme mode switching.

#### File: `assets/translations/en.json`

- **Change Description:** Added a new translation key "dark_theme" with the English translation "Dark Theme". Also, removed a newline at the end of the file.
- **Theme/Color-Related:** Yes (for the new key), No (for newline removal - minor formatting).
- **Non-Theme Changes:** Removed newline at end of file (minor formatting).
- **Notes/Comments:** The primary change is theme-related. The newline removal is a minor formatting change.

#### File: `assets/translations/hi.json`

- **Change Description:** Added a new translation key "dark_theme" with the Hindi translation "डार्क थीम".
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** This is a text content change directly related to supporting theme mode switching.

#### File: `assets/translations/ta.json`

- **Change Description:** Added a new translation key "dark_theme" with the Tamil translation "இருண்ட தீம்".
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** This is a text content change directly related to supporting theme mode switching.

#### File: `lib/blocs/auth_bloc.dart`

- **Change Description:** Updated constructor syntax, renamed a variable, and changed a list emptiness check.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 263: `AuthBloc(AuthState initialState) : super(initialState)` changed to `AuthBloc(super.initialState)`. (Syntax update)
    - Line 274, 460: `stackTrace` renamed to `stacktrace`. (Variable rename)
    - Line 398, 419: `response.error!.length > 0` changed to `response.error!.isNotEmpty`. (Style change)
- **Notes/Comments:** These changes are minor code modernizations and refactorings, not directly related to theme implementation.

#### File: `lib/blocs/bloc_manager.dart`

- **Change Description:** Updated constructor to be `const` and use `super.key`, and updated `createState` method syntax.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 6: `BlocManager({Key? key, required this.child, required this.initState}) : super(key: key);` changed to `const BlocManager({super.key, required this.child, required this.initState});`. (Syntax update)
    - Line 11: `_BlocManagerState createState() => _BlocManagerState();` changed to `State<BlocManager> createState() => _BlocManagerState();`. (Syntax update)
- **Notes/Comments:** These are minor syntax modernizations and refactorings, not directly related to theme implementation.

#### File: `lib/blocs/home_bloc.dart`

- **Change Description:** Added `BuildContext` import, corrected typos in import paths, commented out an unused import, changed `onBoardSessions` to `OnBoardSessions`, added `BuildContext context` to `CreatePostEvent`, removed extra newlines, fixed formatting in constructor, added a cast to `AttendancePercentageResponse?`, passed `event.context` to `homeRepository.createPost`, and fixed formatting in `homeRepository.AcceptFeeAgreement`.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 4: Added `import 'package:flutter/widgets.dart';`.
    - Line 14, 38, 67, 85, 105, 109: Corrected typos in import paths.
    - Line 110: Commented out an unused import.
    - Line 967: Changed `onBoardSessions` to `OnBoardSessions`.
    - Line 1818, 1824: Added a required `BuildContext context` to `CreatePostEvent`.
    - Line 2733, 2752: Removed extra newlines.
    - Line 2765, 2781: Fixed formatting in constructor.
    - Line 3280: Added a cast to `AttendancePercentageResponse?`.
    - Line 4934: Passed `event.context` to `homeRepository.createPost`.
    - Line 5295, 5298: The `response` is passed directly to the state.
    - Line 5324: Fixed formatting in `homeRepository.AcceptFeeAgreement`.
    - Line 5338: Added a newline at the end of the file.
- **Notes/Comments:** These changes are primarily refactoring, import corrections, and minor logic adjustments, not directly related to theme implementation.

#### File: `lib/blocs/theme/theme_state.dart`

- **Change Description:** Enabled Material 3, and updated color constants to use camelCase naming convention.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** All changes are directly related to theme implementation, specifically enabling Material 3 and updating color references to align with new naming conventions.

#### File: `lib/bots/bot.dart`

- **Change Description:** Updated imports for styles and theme extensions, and updated various UI elements to use theme-aware colors and styles.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 126-129: The `sendValue` callback is formatted. (Formatting change)
- **Notes/Comments:** The majority of changes are theme-related, adapting UI colors and styles to the new theme system. A minor formatting change was also present.

#### File: `lib/bots/bot_faq.dart`

- **Change Description:** Updated imports for theme-aware widgets and extensions, wrapped UI in `BlocBuilder` for theme changes, updated colors to use theme-aware context, and replaced `print` with `debugPrint`.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 34, 40: `print` is replaced with `debugPrint`. (Logging change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. A minor logging change was also present.

#### File: `lib/bots/clickable_link.dart`

- **Change Description:** Updated constructor to be `const` and use `super.key`, and updated `createState` method syntax.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 7: `const ClickableTextWidget({required this.text});` changed to `const ClickableTextWidget({super.key, required this.text});`. (Syntax update)
- **Notes/Comments:** This is a minor syntax modernization, not directly related to theme implementation.

#### File: `lib/bots/faq_question_list.dart`

- **Change Description:** Updated imports for theme-aware widgets and extensions, wrapped UI in `BlocBuilder` for theme changes, updated colors and text styles to use theme-aware context, and commented out a navigation line.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 27: The navigation to `Bot()` is commented out. (Logic change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. A minor logic change (commenting out navigation) was also present.

#### File: `lib/bots/messages.dart`

- **Change Description:** Updated commented-out code to use theme-aware colors and styles.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** All changes are within commented-out code and are theme-related.

#### File: `lib/bots/set_goal_bot/bot_messeage_screen.dart`

- **Change Description:** Updated imports for theme-aware widgets and extensions, updated constructor, changed return type of `scrollToBottom`, wrapped `ListView.builder` in `BlocBuilder`, updated colors and text styles to use theme-aware context, and replaced `print` with `debugPrint`.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 20: `const BotMessagesScreen({Key? key, required this.messages, this.sendValue, required this.name}) : super(key: key);` changed to `const BotMessagesScreen({super.key, required this.messages, this.sendValue, required this.name});`. (Syntax update)
    - Line 28: `scrollToBottom()` changed to `void scrollToBottom()`. (Return type change)
    - Line 295: `print` is replaced with `debugPrint`. (Logging change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. Minor syntax and logging changes were also present.

#### File: `lib/bots/set_goal_bot/bot_screen.dart`

- **Change Description:** The entire file content has been commented out.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** The entire file content has been commented out. (Major code change/removal)
- **Notes/Comments:** This is a significant non-theme change, as the entire functionality of this file has been disabled.

#### File: `lib/bots/set_goal_bot/dialogFlow_Bot.dart`

- **Change Description:** Updated imports for styles and theme extensions, removed extra newline, and updated various UI elements to use theme-aware colors and styles.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 11: Removed extra newline. (Formatting change)
    - Line 150-153: The `sendValue` callback is formatted. (Formatting change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. Minor formatting changes were also present.

#### File: `lib/bots/set_goal_bot/dialog_flow_bot.dart`

- **Change Description:** This is a new file, and its entire content is commented out.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** This is a new file, and its entire content is commented out. (New file, but commented out functionality)
- **Notes/Comments:** This file appears to be a new addition, but its functionality is currently disabled. This is a non-theme change.

#### File: `lib/bots/set_goal_bot/dialog_flow_messages.dart`

- **Change Description:** This is a new file, and its entire content is commented out.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** This is a new file, and its entire content is commented out. (New file, but commented out functionality)
- **Notes/Comments:** This file appears to be a new addition, but its functionality is currently disabled. This is a non-theme change.

#### File: `lib/bots/set_goal_bot/graph.dart`

- **Change Description:** Updated imports for theme-aware widgets and extensions, wrapped `ScreenWithLoader` in `BlocBuilder`, updated colors and text styles to use theme-aware context, and updated a list emptiness check.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 671: `domainData?.data?.graphArr.length != 0` changed to `domainData?.data?.graphArr.isNotEmpty ?? false`. (Logic change)
    - Line 624: The `card` widget now takes a `BuildContext` as a parameter. (Signature change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. Minor logic and signature changes were also present.

#### File: `lib/bots/set_goal_bot/singh_bot/Interest_area_page.dart`

- **Change Description:** Updated imports for theme-aware widgets and extensions, updated constructor, initialized `foregroundColor` using theme-aware context, wrapped `Scaffold` and `GridView.builder` in `BlocBuilder`, updated colors and text styles to use theme-aware context, and added null checks for `portfolioState.response?.data`.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 151: The logic for `selectedCategoryIds` is updated. (Logic change)
    - Line 297, 300, 304: Added null checks for `portfolioState.response?.data`. (Logic change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. Minor logic changes were also present.

#### File: `lib/bots/set_goal_bot/singh_bot/singh_bot_message_page.dart`

- **Change Description:** Updated imports for theme-aware widgets and extensions, updated constructor, changed return type of `scrollToBottom`, wrapped `SafeArea` in `BlocBuilder`, updated colors and text styles to use theme-aware context, and replaced `print` with `debugPrint`.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Line 20: `const SinghBotMessagePage({Key? key, required this.messages, this.sendValue, required this.name}) : super(key: key);` changed to `const SinghBotMessagePage({super.key, required this.messages, this.sendValue, required this.name});`. (Syntax update)
    - Line 28: `scrollToBottom()` changed to `void scrollToBottom()`. (Return type change)
    - Line 295: `print` is replaced with `debugPrint`. (Logging change)
    - Line 298: Removed extra newline. (Formatting change)
- **Notes/Comments:** The primary changes are theme-related, adapting UI colors and styles to the new theme system. Minor syntax, logging, and formatting changes were also present.

#### File: `lib/bots/set_goal_bot/singh_bot/singh_bot_page.dart`

- **Change Description:** The entire file content has been commented out.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** The entire file content has been commented out. (Major code change/removal)
- **Notes/Comments:** This is a significant non-theme change, as the entire functionality of this file has been disabled.

#### File: `lib/data/api/api_constants.dart`

- **Change Description:** Removed extra newlines and formatted constants.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 16, 17: Removed extra newlines. (Formatting change)
    - Line 99: Formatted the `REPORT_MODULE_WISE_LEADERBOARD_LIST` constant. (Formatting change)
    - Line 169: Formatted the `Assessment_Details` constant. (Formatting change)
- **Notes/Comments:** These are minor formatting changes and not directly related to theme implementation.

#### File: `lib/data/api/api_response.dart`

- **Change Description:** Updated import for `Log.dart` and updated log message to use string interpolation.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 2: The import for `Log.dart` is changed from `package:masterg/utils/Log.dart` to `package:masterg/utils/log.dart`. (Import path change)
    - Line 28: The log message is updated to use string interpolation. (Code style change)
- **Notes/Comments:** These are minor code style and import changes, not directly related to theme implementation.

#### File: `lib/data/api/api_service.dart`

- **Change Description:** Removed a commented-out import statement.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 1: The commented-out import `//import 'package:dio/adapter.dart';` is removed. (Code cleanup)
- **Notes/Comments:** This is a minor code cleanup, not directly related to theme implementation.

#### File: `lib/data/api/environment.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/request/auth_request/change_password_request.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/request/auth_request/email_request.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 22: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 23-30: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/auth_request/login_request.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 14: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
- **Notes/Comments:** This is a minor code modernization and not directly related to theme implementation.

#### File: `lib/data/models/request/auth_request/signup_request.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 55: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 56-70: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/auth_request/swayam_login_request.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 18: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
- **Notes/Comments:** This is a minor code modernization and not directly related to theme implementation.

#### File: `lib/data/models/request/auth_request/update_user_request.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 102: `relationData?.add(new RelationData.fromJson(v));` changed to `relationData?.add(RelationData.fromJson(v));`. (Syntax update)
    - Line 107: `final Map<String?, dynamic> data = new Map<String?, dynamic>();` changed to `final Map<String?, dynamic> data = <String?, dynamic>{};`. (Syntax update)
    - Line 108-135: Removed `this` keyword when assigning values to the map. (Code style change)
    - Line 137: `final Map<String?, dynamic> relations = new Map<String?, dynamic>();` changed to `final Map<String?, dynamic> relations = <String?, dynamic>{};`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/dealer/submit_reward_req.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 11: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 12: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/home_request/poll_submit_req.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 15: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 16-18: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/home_request/submit_feedback_req.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 27: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 28-33: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/home_request/submit_survey_req.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 14: `questionSubmitted?.add(new QuestionSubmitted.fromJson(v));` changed to `questionSubmitted?.add(QuestionSubmitted.fromJson(v));`. (Syntax update)
    - Line 19: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 20-23: Removed `this` keyword when assigning values to the map. (Code style change)
    - Line 43: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 44-45: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/home_request/track_announcement_request.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 11: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 12: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/home_request/user_program_subscribe.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 11: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 12: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/home_request/user_tracking_activity.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 19: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 20-23: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/request/save_answer_request.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 37: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 38-46: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/auth_response/assessment_report_resp.dart`

- **Change Description:** New file added. This file defines the data model for an assessment report response.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** New file addition.
- **Notes/Comments:** This is a new feature and not related to theme implementation.

#### File: `lib/data/models/response/auth_response/dashboard_view_resp.dart`

- **Change Description:** Added a `void` return type to the `map` function.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 146: `map(Function(dynamic e) param0) {}` changed to `void map(Function(dynamic e) param0) {}`. (Code style change)
- **Notes/Comments:** This is a minor code style change and not directly related to theme implementation.

#### File: `lib/data/models/response/auth_response/login_by_id_response.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/auth_response/login_response.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/auth_response/only_verify_otp_response.dart`

- **Change Description:** This file has been reformatted. The logic is the same.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** Code formatting.
- **Notes/Comments:** This is a minor code style change and not directly related to theme implementation.

#### File: `lib/data/models/response/auth_response/sign_up_response.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword. Also removed a blank line.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 71: Removed a blank line. (Formatting)
    - Line 120: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 121-140: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/auth_response/swayam_login_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 10: `data = json["data"] != null ? new Data.fromJson(json["data"]) : null;` changed to `data = json["data"] != null ? Data.fromJson(json["data"]) : null;`. (Syntax update)
    - Line 14: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 15-18: Removed `this` keyword when assigning values to the map. (Code style change)
    - Line 34: `url = json["url"] != null ? json["url"] : null;` changed to `url = json["url"];`. (Code style change)
    - Line 35: `user = json["user"] != null ? new User.fromJson(json["user"]) : null;` changed to `user = json["user"] != null ? User.fromJson(json["user"]) : null;`. (Syntax update)
    - Line 39: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 40-44: Removed `this` keyword when assigning values to the map. (Code style change)
    - Line 124: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 125-145: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/auth_response/user_session.dart`

- **Change Description:** Added a `void` return type to the `clearSession` function.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 34: `static clearSession() {` changed to `static void clearSession() {`. (Code style change)
- **Notes/Comments:** This is a minor code style change and not directly related to theme implementation.

#### File: `lib/data/models/response/auth_response/verify_otp_resp.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 137: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 138-157: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/general_resp.dart`

- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 13: `final Map<String, dynamic> data = new Map<String, dynamic>();` changed to `final Map<String, dynamic> data = <String, dynamic>{};`. (Syntax update)
    - Line 14-15: Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/add_open_work_response.dart`
- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/add_skill_response.dart`
- **Change Description:** Updated `toJson()` method to use a more modern syntax for creating a map and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/app_version_response.dart`
- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/assessment_details_response.dart`
- **Change Description:** A comment has been formatted.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `//this.certificateId,` changed to `//this.certificateId,`. (Formatting change)
- **Notes/Comments:** This is a minor formatting change and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/assessment_response.dart`
- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new Instruction.fromJson(json['instruction'])` changed to `Instruction.fromJson(json['instruction'])`. (Syntax update)
    - `new Details.fromJson(json['details'])` changed to `Details.fromJson(json['details'])`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/assignment_detail_response.dart`
- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new Assignment.fromJson(v)` changed to `Assignment.fromJson(v)`. (Syntax update)
    - `new Learners.fromJson(v)` changed to `Learners.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/assignment_submissions_response.dart`
- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new AssessmentDetails.fromJson(v)` changed to `AssessmentDetails.fromJson(v)`. (Syntax update)
    - `new SubmissionDetails.fromJson(v)` changed to `SubmissionDetails.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/category_response.dart`
- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson((json['data'] is String)` changed to `Data.fromJson((json['data'] is String)`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new CategoryData.fromJson((v is String) ? converter.json.decode(v) : v)` changed to `CategoryData.fromJson((v is String) ? converter.json.decode(v) : v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/city_state_response.dart`
- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `new Data.fromJson((json['data'] is String)` changed to `Data.fromJson((json['data'] is String)`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new CityStateData.fromJson((v is String) ? converter.json.decode(v) : v)` changed to `CityStateData.fromJson((v is String) ? converter.json.decode(v) : v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/coin_history_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/competition_response.dart`

- **Change Description:** Removed `this` keyword in `updateAppliedStatus`, `resetList`, `addItemList`, and `resetValue` methods within the `CompetitionResponseProvider` class.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Removed `this` keyword for member access. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/content_tags_resp.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListTags.fromJson(v)` changed to `ListTags.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/course_category_list_id_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `name`, `founded` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/create_portfolio_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status` and `message` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/create_post_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `message`, `name`, `founded` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/delete_live_class_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/delete_portfolio_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status` and `message` in `fromJson` and `toJson`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/delete_skill_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed and a newline was removed at the end of the file.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - Removed newline at end of file. (Formatting change)
- **Notes/Comments:** These are minor code modernizations and formatting changes and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/domain_filter_list.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/domain_list_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListElement.fromJson(v)` changed to `ListElement.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/explore_job_details_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ResArr.fromJson(json['resArr'])` changed to `ResArr.fromJson(json['resArr'])`. (Syntax update)
    - `new Program.fromJson(v)` changed to `Program.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/explore_job_list_response.dart`

- **Change Description:** New file added. The file defines data models for `ExploreJobListResponse`, `Data`, `ExploreJob`, `RatingDetail`, and `Skill`. The implementation uses `factory` constructors for `fromJson` and modern Dart syntax for `toJson`. The previous commented-out code has been replaced with a new implementation.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** New file addition, code modernization (factory constructors, null-aware operators, simplified map creation), and removal of commented-out legacy code.
- **Notes/Comments:** This is a significant code refactoring and modernization, not related to theme implementation. The file was essentially rewritten with a cleaner, more idiomatic Dart style.

#### File: `lib/data/models/response/home_response/faculty_response/Attendance_percentage_resp.dart`

- **Change Description:** Minor formatting changes, including adding newlines for better readability and consistent indentation.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** Code formatting.
- **Notes/Comments:** This is a minor code style change and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/faculty_response/matching_jobs_response.dart`

- **Change Description:** Removed `this` keyword in `updateAppliedStatus`, `resetList`, `addItemList`, and `resetValue` methods within the `MatchingJobsProvider` class.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** Code style change (removal of redundant `this` keyword).
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/featured_video_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `name`, `founded`, `tag`, `viewCount`, `likeCount` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/feedback_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/game_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/gcarvaan_post_reponse.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Removed `this` keyword for member access. Replaced `print` with `debugPrint` and added `kDebugMode` check. Added `int` type hint to `getLikeCount`, `incrementLike`, `decrementLike`, `isLiked`, and `updateIsLiked` methods. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Added `import 'package:flutter/foundation.dart';`. (New import)
    - Removed `== null ? null :` for `status`, `name`, `founded`, `viewCount`, `resourceType` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
    - Removed `this` keyword when assigning values to the map and accessing members. (Code style change)
    - `print('clear the data after ${_list?.length}');` changed to `if (kDebugMode) { debugPrint('clear the data after ${_list?.length}'); }`. (Logging change)
    - Added `int` type hint to `getLikeCount(index)`, `incrementLike(index)`, `decrementLike(index)`, `isLiked(index)`, and `updateIsLiked(index, int liked)`. (Type safety)
- **Notes/Comments:** These are minor code modernizations, type safety improvements, and logging changes, not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/get_certificates_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/get_comment_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `userStatus` in `fromJson` and `toJson`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/get_course_leaderboard_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/get_course_modules_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/get_courses_resp.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `description` in `fromJson` and `toJson`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/get_kpi_analysis_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/get_module_leaderboard_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/greels_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Removed `this` keyword for member access. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `name`, `founded`, `tag`, `likeCount`, `startDate`, `endDate`, `profile_image`, `user_like_trackings_id`, `resource_type`, `thumbnail_url` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
    - Removed `this` keyword when accessing members. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/joy_category_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `name`, `founded` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/joy_content_list_response.dart`

- **Change Description:** New file added. This file defines the data model for a joy content list response.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** New file addition.
- **Notes/Comments:** This is a new feature and not related to theme implementation.

#### File: `lib/data/models/response/home_response/language_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/learning_space_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new LearningData.fromJson(json['data'])` changed to `LearningData.fromJson(json['data'])`. (Syntax update)
    - `new Trainers.fromJson(v)` changed to `Trainers.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/list_portfolio_responsed.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status` in `fromJson` and `toJson`. (Code style change)
    - `data == null ? null : data!.toJson()` changed to `data?.toJson()`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/live_class_resp.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/map_interest_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields and removed `null` assignments. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `name`, `founded` in `fromJson` and `toJson`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/master_language_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListLanguage.fromJson(v)` changed to `ListLanguage.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/my_assessment_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use a more modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new AssessmentList.fromJson(v)` changed to `AssessmentList.fromJson(v)`. (Syntax update)
    - `difficultyLevel = '${tr('${json['difficulty_level'].toString().toLowerCase()}')}';` changed to `difficultyLevel = tr(json['difficulty_level'].toString().toLowerCase());`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/my_assignment_response.dart`

- **Change Description:** Updated `fromJson()` method to remove redundant null checks for non-nullable fields. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `score` in `fromJson`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.
#### File: `lib/data/providers/home_provider.dart`

- **Change Description:** Updated import aliases, string concatenations, list emptiness checks, map initialization syntax, variable type declarations, and logging statements. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `import 'dart:math' as Math;` changed to `import 'dart:math' as math;`. (Naming convention)
    - `import 'package:masterg/utils/Log.dart';` changed to `import 'package:masterg/utils/log.dart';`. (Import path case)
    - `(response.data["error"] as List).length != 0` changed to `(response.data["error"] as List).isNotEmpty` throughout the file. (Code style)
    - String concatenations replaced with string interpolation for better readability throughout the file.
    - `Map<String, dynamic> data = Map();` changed to `Map<String, dynamic> data = {};`. (Syntax update)
    - `var header;` changed to `Map<String, String> header;`. (Type declaration)
    - `print` statements replaced with `debugPrint` for better logging practices.
    - Added proper logging with context in `getFacultyBatchDetails` method.
- **Notes/Comments:** These are extensive code modernizations and style improvements, not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/new_portfolio_response.dart`

- **Change Description:** Updated null-aware operators in `Resume` class `fromJson` method.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 341: `json["url"] != null ? json["url"] : ''` changed to `json["url"] ?? ''`. (Null-aware operator)
    - Line 342: `json["id"] != null ? json["id"] : 0` changed to `json["id"] ?? 0`. (Null-aware operator)
- **Notes/Comments:** These are minor code modernizations using null-aware operators, not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/notification_resp.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/notificaton_resp.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use modern syntax for creating objects and maps and removed `this` keyword.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
    - `this.data?.toJson()` used instead of `this.data!.toJson()`. (Null-aware operator)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/onboard_sessions.dart`

- **Change Description:** Updated class name from `onBoardSessions` to `OnBoardSessions`, updated `fromJson()` and `toJson()` methods to use modern syntax, removed `this` keyword, improved formatting, and enhanced code style. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Class name changed from `onBoardSessions` to `OnBoardSessions`. (Naming convention)
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new Liveclass.fromJson(v)` changed to `Liveclass.fromJson(v)`. (Syntax update)
    - Improved constructor formatting and spacing.
    - Enhanced conditional logic formatting in `refreshList` method.
- **Notes/Comments:** These are significant code modernizations and naming convention improvements, not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/popular_courses_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use modern syntax for creating objects and maps and removed `this` keyword throughout all classes. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map throughout all classes. (Code style change)
    - `new ShortTerm.fromJson(v)`, `new Recommended.fromJson(v)`, etc. changed to remove `new` keyword. (Syntax update)
- **Notes/Comments:** These are extensive code modernizations across multiple classes, not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/portfolio_competition_response.dart`

- **Change Description:** Improved formatting in `fromJson` method and `toJson` method.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Line 98-99: Improved line formatting for `competitionLevel` assignment. (Formatting change)
    - Lines 119-124: Improved spacing and formatting in `toJson` method. (Formatting change)
- **Notes/Comments:** These are minor formatting improvements, not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/post_comment_response.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to remove redundant null checks for non-nullable fields. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `== null ? null :` for `status`, `message`, `name`, `founded` in `fromJson` and `toJson`. (Code style change)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/program_list_reponse.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ProgramListElement.fromJson(v)` changed to `ProgramListElement.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/report_content_response.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/models/response/home_response/reported_users_resp.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/reward_resp.dart`

- **Change Description:** Updated `fromJson()` and `toJson()` methods to use modern syntax for creating objects and maps and removed `this` keyword. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - `new Data.fromJson(json['data'])` changed to `Data.fromJson(json['data'])`. (Syntax update)
    - `new Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Syntax update)
    - Removed `this` keyword when assigning values to the map. (Code style change)
    - `new ListData.fromJson(v)` changed to `ListData.fromJson(v)`. (Syntax update)
- **Notes/Comments:** These are minor code modernizations and not directly related to theme implementation.

#### File: `lib/data/models/response/home_response/save_answer_response.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/providers/auth_provider.dart`

- **Change Description:** Updated error handling, list emptiness checks, string concatenation, and file mode. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Enhanced error handling in catch block with DioException check. (Error handling improvement)
    - `(response.data["error"] as List).length != 0` changed to `(response.data["error"] as List).isNotEmpty`. (Code style)
    - `ApiConstants.CITY_API + "/$stateId"` changed to `"${ApiConstants.CITY_API}/$stateId"`. (String interpolation)
- **Notes/Comments:** These are code improvements and modernizations, not directly related to theme implementation.

#### File: `lib/main.dart`

- **Change Description:** Major restructuring of app initialization, theme integration, error handling improvements, and code modernizations. Also, the file mode is changed.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Removed `package:get/get.dart` import and replaced GetMaterialApp with MaterialApp. (Framework change)
    - Added comprehensive error handling for Hive initialization. (Error handling)
    - Restructured app initialization order and moved EasyLocalization to top level. (Architecture change)
    - Added ThemeBloc to BlocProvider list. (Theme integration)
    - Enhanced error handling in runZonedGuarded. (Error handling)
    - Improved logging with debugPrint instead of print. (Logging improvement)
    - Added proper null safety handling throughout. (Code safety)
    - Restructured deep linking initialization. (Feature enhancement)
- **Notes/Comments:** This file contains significant theme-related changes including ThemeBloc integration and BlocBuilder for theme state management, along with major architectural improvements.

#### File: `lib/utils/theme/theme_manager.dart`

- **Change Description:** Entire file deleted.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** This theme management utility file was completely removed, indicating a restructuring of the theme management approach.

#### File: `lib/utils/theme/theme_extensions.dart`

- **Change Description:** Updated color constant references to use camelCase naming convention and added new theme-aware colors.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** All changes are theme-related, updating color references to align with new naming conventions and adding new theme-aware color properties like `customPurple` and `lightBlueBg`.

#### File: `lib/utils/resource/colors.dart`

- **Change Description:** Major refactoring of color constants, updated naming conventions from UPPER_CASE to camelCase, removed deprecated constants, added theme-aware color methods, and improved color management structure. Also, the file mode is changed.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Simplified hex color conversion logic by removing unnecessary bitwise operations. (Code optimization)
- **Notes/Comments:** This is a comprehensive theme-related refactoring that modernizes the color system, introduces theme-aware color methods, and establishes a better foundation for dark/light theme support.

#### File: `lib/utils/theme/theme_aware_widgets.dart`

- **Change Description:** Entire file deleted.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:** None
- **Notes/Comments:** This theme-aware widget utility file was completely removed, indicating a restructuring of the theme widget approach.

#### File: `lib/pages/auth_pages/bottomSheet_login_flow/dark_preboaring.dart`

- **Change Description:** Updated imports, constructor syntax, color references to use theme-aware colors, and improved code formatting.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - Updated import paths to use lowercase naming convention. (Import path change)
    - `const FullScreenPreboarding({Key? key}) : super(key: key);` changed to `const FullScreenPreboarding({super.key});`. (Constructor syntax)
    - Improved conditional statement formatting. (Code style)
    - Removed string interpolation for simple variables. (Code style)
- **Notes/Comments:** The primary changes are theme-related, replacing hardcoded color references with theme-aware color properties from `context.appColors`. Minor code style improvements were also made.

#### File: `lib/data/providers/announcement_detail_provider.dart`

- **Change Description:** Code formatting improvements for method call structure.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - Removed extra blank line. (Code formatting)
    - Fixed method call formatting by moving closing parenthesis and semicolon to same line. (Code formatting)
- **Notes/Comments:** These are minor code formatting improvements and not directly related to theme implementation.

#### File: `lib/data/providers/base_state.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/data/providers/menu_provider.dart`

- **Change Description:** Added explicit return type to method.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - `updateList(BottomBarResponse response)` changed to `void updateList(BottomBarResponse response)`. (Return type specification)
- **Notes/Comments:** This is a minor code improvement and not directly related to theme implementation.

#### File: `lib/data/repositories/auth_repository.dart`

- **Change Description:** Updated import, logging method, and file mode. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Added `import 'package:flutter/foundation.dart';`. (Import addition)
    - `print('$stackTrace');` changed to `debugPrint('$stackTrace');`. (Logging improvement)
- **Notes/Comments:** These are code improvements for better logging practices and not directly related to theme implementation.

#### File: `lib/data/repositories/home_repository.dart`

- **Change Description:** Extensive code modernization including import reorganization, null-aware operators, string interpolation, logging improvements, and theme integration. Also, the file mode is changed.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Reorganized imports and updated import paths to use lowercase naming convention. (Import organization)
    - `response.body == null ? "Something went wrong:" as List<String>? : response.body` changed to `response.body ?? "Something went wrong:" as List<String>?`. (Null-aware operators)
    - String concatenation changed to string interpolation throughout. (Code style)
    - `print()` changed to `debugPrint()` throughout. (Logging improvement)
    - `throw e;` changed to `rethrow;`. (Exception handling)
    - Added BuildContext parameter to createPost method. (Method signature change)
    - Various code formatting and style improvements. (Code style)
- **Notes/Comments:** This file contains significant theme-related changes including integration of theme-aware colors and styles, along with extensive code modernization. The createPost method now uses theme-aware colors for snackbar styling.

#### File: `lib/dependency_injections.dart`

- **Change Description:** File mode changed from 100755 to 100644. No content changes.
- **Theme/Color-Related:** No
- **Non-Theme Changes:** File mode change.
- **Notes/Comments:** This is a metadata change and not related to theme implementation.

#### File: `lib/local/pref/Preference.dart`

- **Change Description:** Updated imports, constructor syntax, null-aware operators, logging improvements, and code modernization. Also, the file mode is changed.
- **Theme/Color-Related:** No
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Updated import path to use lowercase naming convention. (Import path change)
    - Added `import 'package:flutter/foundation.dart';`. (Import addition)
    - `getInstance()` changed to `Preference getInstance()`. (Return type specification)
    - `new Preference()` changed to `Preference()`. (Constructor syntax)
    - `Map<String, dynamic>()` changed to `<String, dynamic>{}`. (Map creation syntax)
    - Multiple null checks replaced with null-aware operators. (Code style)
    - `print()` changed to `debugPrint()`. (Logging improvement)
    - Added name parameter to Log.v call. (Logging enhancement)
- **Notes/Comments:** These are code modernization improvements and not directly related to theme implementation.

#### File: `lib/pages/auth_pages/choose_language.dart`

- **Change Description:** Major theme integration with BlocBuilder, updated imports, constructor syntax, and comprehensive color system integration. Also, the file mode is changed.
- **Theme/Color-Related:** Yes
- **Non-Theme Changes:**
    - File mode changed from 100755 to 100644.
    - Updated import paths to use lowercase naming convention. (Import path change)
    - `ChooseLanguage({Key? key, required this.showEdulystLogo}) : super(key: key);` changed to `const ChooseLanguage({super.key, required this.showEdulystLogo});`. (Constructor syntax)
    - `_ChooseLanguageState createState() => _ChooseLanguageState();` changed to `State<ChooseLanguage> createState() => _ChooseLanguageState();`. (Return type specification)
    - Added missing braces to for loop. (Code style)
    - Improved code formatting and structure. (Code style)
- **Notes/Comments:** This file contains major theme-related changes including integration of ThemeBloc, BlocBuilder for theme state management, and comprehensive replacement of hardcoded colors with theme-aware color properties. The gradient button was replaced with theme-aware primary color.
