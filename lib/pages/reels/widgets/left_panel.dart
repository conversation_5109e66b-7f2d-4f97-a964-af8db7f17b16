import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:masterg/pages/ghome/widget/read_more.dart';
import 'package:masterg/pages/reels/widgets/column_social_icon.dart';
import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';

class LeftPanel extends StatelessWidget {
  final String? name;
  final String? caption;
  final int? viewCounts;
  final String? createdAt;
  final String? profileImg;
  final String? userStatus;
  final Function? expanded;
  const LeftPanel(
      {super.key,
      required this.size,
      this.name,
      this.caption,
      this.viewCounts,
      this.createdAt,
      this.userStatus,
      this.profileImg,
      this.expanded});

  final Size size;

  @override
  Widget build(BuildContext context) {
    var millis = int.parse(createdAt!);
    DateTime date = DateTime.fromMillisecondsSinceEpoch(
      millis * 1000,
    );

    final now = DateTime.now();

    // String calculateTimeDifferenceBetween(
    //     DateTime startDate, DateTime endDate) {
    //   int seconds = endDate.difference(startDate).inSeconds;
    //   if (seconds < 60)
    //     {
    //        if(seconds.abs() < 4) return 'Just Now';
    //     return '${seconds.abs()} s';
    //     }
    //   else if (seconds >= 60 && seconds < 3600)
    //     return '${startDate.difference(endDate).inMinutes.abs()} m';
    //   else if (seconds >= 3600 && seconds < 86400)
    //     return '${startDate.difference(endDate).inHours.abs()} h';
    //   else
    //     return '${startDate.difference(endDate).inDays.abs()} d';
    // }

    String calculateTimeDifferenceBetween(
        DateTime startDate, DateTime endDate) {
      int seconds = endDate.difference(startDate).inSeconds;
      if (seconds < 60) {
        if (seconds.abs() < 4) return tr('now');
        return '${seconds.abs()} ${tr('second')}';
      } else if (seconds >= 60 && seconds < 3600)
        return '${startDate.difference(endDate).inMinutes.abs()} ${tr('mins')}';
      else if (seconds >= 3600 && seconds < 86400)
        return '${startDate.difference(endDate).inHours.abs()} ${tr('hour')}';
      else {
        // convert day to month
        int days = startDate.difference(endDate).inDays.abs();
        if (days < 30 && days > 7) {
          return '${(startDate.difference(endDate).inDays ~/ 7).abs()} ${tr('week')}';
        }
        if (days > 30) {
          int month = (startDate.difference(endDate).inDays ~/ 30).abs();
          return '$month ${tr('months')}';
        } else {
          return '${startDate.difference(endDate).inDays.abs()} ${tr('day')}';
        }
      }
    }

    return Container(
      width: size.width * 0.8,
      height: size.height,
      decoration: BoxDecoration(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 10,
              ),
              getProfile(profileImg, userStatus == 'active'),
              SizedBox(
                width: 10,
              ),
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: width(context) * 0.55,
                      child: Text(name!,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Styles.semibold(
                              size: 14,
                              color: userStatus != "active"
                                  ? context.appColors.grey3
                                      .withValues(alpha: 0.8)
                                  : context.appColors.primaryForeground)),
                    ),
                    Text(
                      calculateTimeDifferenceBetween(
                          DateTime.parse(date.toString().substring(0, 19)),
                          now),
                      style: Styles.regular(
                          size: 12, color: context.appColors.primaryForeground),
                    ),
                  ]),
            ],
          ),
          SizedBox(
            height: 3,
          ),
          caption != 'null'
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: ReadMoreText(
                    height: height(context) * 0.4,
                    text: caption ?? '',
                    color: context.appColors.primaryForeground,
                    showGradient: false,
                    onChange: (bool expandedValue) {
                      expanded!(expandedValue);
                    },
                  ),
                )
              // ? Text(
              //     caption!,
              //     style: Styles.regular(size: 12, color: context.appColors.white),
              //   )
              : SizedBox(),
          SizedBox(
            height: 3,
          ),
          Row(children: [
            SizedBox(
              width: 10,
            ),
            Text(
              viewCounts == 1 || viewCounts == 0
                  ? '$viewCounts ${tr('view')}'
                  : '$viewCounts ${tr('views')}',
              style: Styles.regular(
                  size: 12, color: context.appColors.primaryForeground),
            ),
            // if (viewCounts! > 1 &&
            //     Preference.getInt(Preference.APP_LANGUAGE) == 1)
            //   Text(
            //     Preference.getInt(Preference.APP_LANGUAGE) == 1 ? 's' : '',
            //     style: Styles.regular(size: 12, color: context.appColors.white),
            //   ),
          ])
        ],
      ),
    );
  }
}
