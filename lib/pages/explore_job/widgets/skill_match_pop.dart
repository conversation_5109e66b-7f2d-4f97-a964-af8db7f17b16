import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/bloc_manager.dart';

import 'package:masterg/utils/styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/theme/theme_extensions.dart';
import 'package:masterg/utils/utility.dart';
import 'package:provider/provider.dart';

import '../../../blocs/home_bloc.dart';
import '../../../data/api/api_service.dart';

import '../../../utils/Log.dart';
import '../explore_job_proivder.dart';

class SkillMatchPop extends StatelessWidget {
  final int index;

  final Function(int skillId) onSkillAdd;

  const SkillMatchPop(
      {super.key, required this.index, required this.onSkillAdd});

  @override
  Widget build(BuildContext context) {
    ExploreJobProvider jobProvider = Provider.of<ExploreJobProvider>(context);

    return BlocManager(
      initState: (context) {},
      child: BlocListener<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is AddSkillState) handleAddSkill(state);
        },
        child: Padding(
          padding:
              const EdgeInsets.only(left: 20, right: 20, bottom: 40, top: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'skills_required_emp',
                style: Styles.getBoldThemeStyle(context, size: 16),
              ).tr(),
              RichText(
                  text: TextSpan(children: [
                TextSpan(
                    text:
                        "${jobProvider.filterJobs?[index].skills?.where((element) => element.userMatch != null && element.userMatch! > 0).length} ${tr('skills')}",
                    // text: '5 out of 5 skills',
                    style: Styles.textExtraBoldUnderline(size: 12)),
                TextSpan(text: ' ', style: Styles.textExtraBoldUnderline()),
                TextSpan(
                    text: ' ${tr('profile_found_matching_with_job')} ',
                    style: Styles.regular(
                        size: 12, color: context.appColors.grey3)),
              ])),
              Divider(),
              ListView.builder(
                  shrinkWrap: true,
                  itemCount: jobProvider.filterJobs?[index].skills?.length,
                  itemBuilder: (context, skillIndex) {
                    bool isMatched = (int.tryParse(
                                '${jobProvider.filterJobs?[index].skills?[skillIndex].userMatch}') ??
                            0) >
                        0;
                    return Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 2, vertical: 12),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: <Widget>[
                          // Text(
                          //     '${jobProvider.filterJobs?[index].skills?[skillIndex].toJson()}'),
                          isMatched
                              ? SvgPicture.asset(
                                  'assets/images/video_resume_key_complete.svg',
                                )
                              : SvgPicture.asset(
                                  'assets/images/video_resume_key_error.svg'),
                          SizedBox(width: 10),
                          SizedBox(
                            width: width(context) *
                                (Utility().isRTL(context) ? 0.5 : 0.54),
                            child: Text(
                              '${jobProvider.filterJobs?[index].skills?[skillIndex].skills}',
                              style: Styles.semibold(
                                size: 14,
                                lineHeight: 1.4,
                                color: Color.fromARGB(255, 79, 79, 81),
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!isMatched) ...[
                            SizedBox(width: 10),
                            InkWell(
                              onTap: () {
                                Map<String, dynamic> data = {};
                                data['skill_id'] = jobProvider
                                    .filterJobs?[index].skills?[skillIndex].id;
                                data['self_proficiency'] = 25;
                                onSkillAdd(jobProvider.filterJobs![index]
                                    .skills![skillIndex].id!);
                                BlocProvider.of<HomeBloc>(context)
                                    .add(AddSkillEvent(data: data));
                              },
                              child: SizedBox(
                                child: Text(
                                  'add_this_skill',
                                  style: Styles.regular(
                                    size: 12,
                                    color: context.appColors.primary,
                                  ),
                                ).tr(),
                              ),
                            )
                          ]
                        ],
                      ),
                    );
                  })
            ],
          ),
        ),
      ),
    );
  }

  void handleAddSkill(AddSkillState state) {
    switch (state.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading Add  Skill....................");

        break;

      case ApiStatus.SUCCESS:
        Log.v("Success Add  Skill....................");

        // ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        //   content: Text('${state.response?.data}'),
        // ));
        // onSkillAdd(skillId: addedSkillId);

        break;
      case ApiStatus.ERROR:
        Log.v("Error Add Skill....................");

        break;
      case ApiStatus.INITIAL:
        break;
    }
  }
}
